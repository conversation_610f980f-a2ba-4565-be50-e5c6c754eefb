import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_text_style.dart';

class EnhancedTypingSuggestionsOverlay extends StatefulWidget {
  final List<SuggestionItem> suggestions;
  final Function(SuggestionItem) onSuggestionTap;
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  final Function(String)? onSearchChanged;
  final bool showSearchBar;

  const EnhancedTypingSuggestionsOverlay({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
    this.isLoading = false,
    this.onLoadMore,
    this.hasMore = false,
    this.onSearchChanged,
    this.showSearchBar = false,
  });

  @override
  State<EnhancedTypingSuggestionsOverlay> createState() => _EnhancedTypingSuggestionsOverlayState();
}

class _EnhancedTypingSuggestionsOverlayState extends State<EnhancedTypingSuggestionsOverlay> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: widget.showSearchBar ? MediaQuery.of(context).size.height * 0.7 : 200,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showSearchBar) _buildSearchBar(),
          Expanded(
            child: _buildSuggestionsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: AppSearchField(
        textEditingController: _searchController,
        hintText: 'search to tag stores, members & products',
        isAutoFocus: true,
        onChangeText: (value) {
          if (widget.onSearchChanged != null) {
            widget.onSearchChanged!(value);
          }
        },
        onTapSuffix: () {
          if (widget.onSearchChanged != null) {
            widget.onSearchChanged!(_searchController.text);
          }
        },
      ),
    );
  }

  Widget _buildSuggestionsList() {
    if (widget.suggestions.isEmpty && !widget.isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('No results found'),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.suggestions.length + (widget.isLoading ? 1 : 0) + (widget.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < widget.suggestions.length) {
          return _buildSuggestionItem(widget.suggestions[index]);
        } else if (widget.isLoading) {
          return _buildLoadingItem();
        } else if (widget.hasMore) {
          return _buildLoadMoreItem();
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSuggestionItem(SuggestionItem suggestion) {
    return InkWell(
      onTap: () => widget.onSuggestionTap(suggestion),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Profile image
            CustomImageContainer(
              width: 42,
              height: 42,
              imageUrl: suggestion.imageUrl,
              imageType: _getImageType(suggestion.type),
            ),
            const SizedBox(width: 12),
            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    suggestion.primaryText ?? '',
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (suggestion.secondaryText != null &&
                      suggestion.secondaryText!.isNotEmpty)
                    Text(
                      suggestion.secondaryText!,
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            // Type indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getTypeColor(suggestion.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _getTypeLabel(suggestion.type),
                style: AppTextStyle.contentText0(
                  textColor: _getTypeColor(suggestion.type),
                ).copyWith(fontSize: 10),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingItem() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  Widget _buildLoadMoreItem() {
    return InkWell(
      onTap: widget.onLoadMore,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'Load more...',
            style: AppTextStyle.contentText0(
              textColor: AppColors.brandBlack,
            ),
          ),
        ),
      ),
    );
  }

  CustomImageContainerType _getImageType(String? type) {
    switch (type) {
      case 'USER':
        return CustomImageContainerType.user;
      case 'STORE':
        return CustomImageContainerType.store;
      case 'PRODUCT':
        return CustomImageContainerType.product;
      default:
        return CustomImageContainerType.user;
    }
  }

  Color _getTypeColor(String? type) {
    switch (type) {
      case 'USER':
        return AppColors.orange;
      case 'STORE':
        return AppColors.brandGreen;
      case 'PRODUCT':
        return AppColors.lightGreen;
      default:
        return AppColors.writingBlack1;
    }
  }

  String _getTypeLabel(String? type) {
    switch (type) {
      case 'USER':
        return 'User';
      case 'STORE':
        return 'Store';
      case 'PRODUCT':
        return 'Product';
      default:
        return '';
    }
  }
}
